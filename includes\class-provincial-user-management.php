<?php
/**
 * Provincial User Management Class
 * 
 * Handles user management interface and functionality for Provincial Administration Manager
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Provincial_User_Management {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_ajax_create_provincial_user', array($this, 'ajax_create_user'));
        add_action('wp_ajax_update_user_districts', array($this, 'ajax_update_user_districts'));
        add_action('wp_ajax_delete_provincial_user', array($this, 'ajax_delete_user'));
    }
    
    /**
     * Initialize
     */
    public function init() {
        // Add any initialization code here if needed
    }
    
    /**
     * User management page
     */
    public function user_management_page() {
        // Handle form submissions
        if (isset($_POST['action'])) {
            $this->handle_form_submission();
        }
        
        include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/user-management.php';
    }
    
    /**
     * Handle form submissions
     */
    private function handle_form_submission() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.'));
        }
        
        if (!wp_verify_nonce($_POST['provincial_user_nonce'], 'provincial_user_management')) {
            wp_die(__('Security check failed.'));
        }
        
        $action = sanitize_text_field($_POST['action']);
        
        switch ($action) {
            case 'create_user':
                $this->create_user();
                break;
            case 'update_districts':
                $this->update_user_districts();
                break;
            case 'delete_user':
                $this->delete_user();
                break;
        }
    }
    
    /**
     * Create new user
     */
    private function create_user() {
        $username = sanitize_user($_POST['username']);
        $email = sanitize_email($_POST['email']);
        $first_name = sanitize_text_field($_POST['first_name']);
        $last_name = sanitize_text_field($_POST['last_name']);
        $user_role = sanitize_text_field($_POST['user_role']);
        $password = $_POST['password'];
        $assigned_districts = isset($_POST['assigned_districts']) ? array_map('intval', $_POST['assigned_districts']) : array();
        
        // Validate inputs
        if (empty($username) || empty($email) || empty($password)) {
            add_settings_error('esp_messages', 'esp_message', __('Username, email, and password are required.', 'esp-admin-manager'), 'error');
            return;
        }
        
        if (!in_array($user_role, array('provincial_user', 'district_user'))) {
            add_settings_error('esp_messages', 'esp_message', __('Invalid user role selected.', 'esp-admin-manager'), 'error');
            return;
        }
        
        // Create user
        $user_id = wp_create_user($username, $password, $email);
        
        if (is_wp_error($user_id)) {
            add_settings_error('esp_messages', 'esp_message', $user_id->get_error_message(), 'error');
            return;
        }
        
        // Update user meta
        wp_update_user(array(
            'ID' => $user_id,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'role' => $user_role
        ));
        
        // Set user type
        update_user_meta($user_id, 'provincial_user_type', $user_role === 'provincial_user' ? 'provincial' : 'district');
        
        // Assign districts for district users
        if ($user_role === 'district_user' && !empty($assigned_districts)) {
            update_user_meta($user_id, 'assigned_districts', $assigned_districts);
        }
        
        add_settings_error('esp_messages', 'esp_message', __('User created successfully.', 'esp-admin-manager'), 'updated');
    }
    
    /**
     * Update user district assignments
     */
    private function update_user_districts() {
        $user_id = intval($_POST['user_id']);
        $assigned_districts = isset($_POST['assigned_districts']) ? array_map('intval', $_POST['assigned_districts']) : array();
        
        if (!$user_id) {
            add_settings_error('esp_messages', 'esp_message', __('Invalid user ID.', 'esp-admin-manager'), 'error');
            return;
        }
        
        update_user_meta($user_id, 'assigned_districts', $assigned_districts);
        add_settings_error('esp_messages', 'esp_message', __('District assignments updated successfully.', 'esp-admin-manager'), 'updated');
    }
    
    /**
     * Delete user
     */
    private function delete_user() {
        $user_id = intval($_POST['user_id']);
        
        if (!$user_id) {
            add_settings_error('esp_messages', 'esp_message', __('Invalid user ID.', 'esp-admin-manager'), 'error');
            return;
        }
        
        $user = get_user_by('id', $user_id);
        if (!$user || !in_array($user->roles[0], array('provincial_user', 'district_user'))) {
            add_settings_error('esp_messages', 'esp_message', __('Invalid user or insufficient permissions.', 'esp-admin-manager'), 'error');
            return;
        }
        
        if (wp_delete_user($user_id)) {
            add_settings_error('esp_messages', 'esp_message', __('User deleted successfully.', 'esp-admin-manager'), 'updated');
        } else {
            add_settings_error('esp_messages', 'esp_message', __('Failed to delete user.', 'esp-admin-manager'), 'error');
        }
    }
    
    /**
     * Get all provincial and district users
     */
    public function get_provincial_users() {
        $users = get_users(array(
            'role__in' => array('provincial_user', 'district_user'),
            'orderby' => 'display_name',
            'order' => 'ASC'
        ));
        
        return $users;
    }
    
    /**
     * Get all districts for assignment
     */
    public function get_all_districts() {
        $districts = get_posts(array(
            'post_type' => 'esp_district',
            'numberposts' => -1,
            'post_status' => 'publish',
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        
        return $districts;
    }
    
    /**
     * AJAX handler for creating user
     */
    public function ajax_create_user() {
        check_ajax_referer('provincial_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions.'));
        }
        
        // Process user creation
        $this->create_user();
        
        wp_die();
    }
    
    /**
     * AJAX handler for updating user districts
     */
    public function ajax_update_user_districts() {
        check_ajax_referer('provincial_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions.'));
        }
        
        // Process district update
        $this->update_user_districts();
        
        wp_die();
    }
    
    /**
     * AJAX handler for deleting user
     */
    public function ajax_delete_user() {
        check_ajax_referer('provincial_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions.'));
        }
        
        // Process user deletion
        $this->delete_user();
        
        wp_die();
    }
    
    /**
     * Filter content by user access - no restrictions applied
     * NOTE: All restrictions removed - all users see all content
     */
    public static function filter_content_by_user_access($content, $user_id = null) {
        // No filtering - all users see all content
        return $content;
    }
    
    /**
     * Check if user can edit specific content - no restrictions applied
     * NOTE: All restrictions removed - all users can edit all content
     */
    public static function user_can_edit_content($post_id, $user_id = null) {
        // No restrictions - all users can edit all content
        return true;
    }
}
