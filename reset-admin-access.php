<?php
/**
 * Reset Administrator Access Script
 * 
 * This script completely removes all user restrictions and restores
 * full WordPress administrator access.
 * 
 * Run this by visiting: yoursite.com/wp-content/plugins/dakoii-prov-administration-manager/reset-admin-access.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Security check - only allow administrators to run this
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Only administrators can run this script.');
}

echo '<h1>WordPress Administrator Access Reset</h1>';
echo '<p>Resetting all user restrictions and restoring default WordPress functionality...</p>';

// 1. Remove all user level restrictions
$admin_users = get_users(array('role' => 'administrator'));
echo '<h2>Removing User Level Restrictions</h2>';

foreach ($admin_users as $user) {
    echo '<p>Processing user: ' . $user->display_name . ' (ID: ' . $user->ID . ')</p>';
    
    // Remove restrictive user meta
    delete_user_meta($user->ID, '_esp_user_level');
    delete_user_meta($user->ID, 'esp_user_level');
    delete_user_meta($user->ID, '_user_level_restriction');
    delete_user_meta($user->ID, 'provincial_user_type');
    
    // Set to provincial admin if needed
    update_user_meta($user->ID, 'provincial_user_type', 'provincial');
    
    echo '<p>✓ Removed restrictions for ' . $user->display_name . '</p>';
}

// 2. Restore all administrator capabilities
echo '<h2>Restoring Administrator Capabilities</h2>';

$admin_role = get_role('administrator');
if ($admin_role) {
    // Complete set of WordPress default administrator capabilities
    $default_admin_caps = array(
        'switch_themes' => true,
        'edit_themes' => true,
        'activate_plugins' => true,
        'edit_plugins' => true,
        'edit_users' => true,
        'edit_files' => true,
        'manage_options' => true,
        'moderate_comments' => true,
        'manage_categories' => true,
        'manage_links' => true,
        'upload_files' => true,
        'import' => true,
        'unfiltered_html' => true,
        'edit_posts' => true,
        'edit_others_posts' => true,
        'edit_published_posts' => true,
        'publish_posts' => true,
        'edit_pages' => true,
        'read' => true,
        'level_10' => true,
        'level_9' => true,
        'level_8' => true,
        'level_7' => true,
        'level_6' => true,
        'level_5' => true,
        'level_4' => true,
        'level_3' => true,
        'level_2' => true,
        'level_1' => true,
        'level_0' => true,
        'edit_others_pages' => true,
        'edit_published_pages' => true,
        'publish_pages' => true,
        'delete_pages' => true,
        'delete_others_pages' => true,
        'delete_published_pages' => true,
        'delete_posts' => true,
        'delete_others_posts' => true,
        'delete_published_posts' => true,
        'delete_private_posts' => true,
        'edit_private_posts' => true,
        'read_private_posts' => true,
        'delete_private_pages' => true,
        'edit_private_pages' => true,
        'read_private_pages' => true,
        'delete_users' => true,
        'create_users' => true,
        'unfiltered_upload' => true,
        'edit_dashboard' => true,
        'update_plugins' => true,
        'delete_plugins' => true,
        'install_plugins' => true,
        'update_themes' => true,
        'install_themes' => true,
        'update_core' => true,
        'list_users' => true,
        'remove_users' => true,
        'promote_users' => true,
        'edit_theme_options' => true,
        'delete_themes' => true,
        'export' => true
    );

    foreach ($default_admin_caps as $cap => $grant) {
        $admin_role->add_cap($cap, $grant);
    }
    
    echo '<p>✓ Restored all default administrator capabilities</p>';
} else {
    echo '<p>❌ Administrator role not found!</p>';
}

// 3. Clear any problematic options
echo '<h2>Clearing Problematic Options</h2>';

delete_option('esp_user_capabilities_updated');
delete_option('esp_user_capabilities_updated_v2');
delete_option('esp_menu_restrictions');

echo '<p>✓ Cleared problematic options</p>';

// 4. Force refresh capabilities
echo '<h2>Refreshing User Capabilities</h2>';

wp_cache_delete('alloptions', 'options');
wp_cache_flush();

echo '<p>✓ Cleared WordPress cache</p>';

echo '<h2>✅ Reset Complete!</h2>';
echo '<p><strong>All administrator restrictions have been removed.</strong></p>';
echo '<p>You should now see all default WordPress menu items including:</p>';
echo '<ul>';
echo '<li>✅ Posts</li>';
echo '<li>✅ Pages</li>';
echo '<li>✅ Comments</li>';
echo '<li>✅ Media Library</li>';
echo '<li>✅ Appearance</li>';
echo '<li>✅ Plugins</li>';
echo '<li>✅ Users</li>';
echo '<li>✅ Tools</li>';
echo '<li>✅ Settings</li>';
echo '</ul>';

echo '<p><a href="' . admin_url() . '">← Return to WordPress Admin</a></p>';

// Log the reset action
error_log('WordPress Administrator Access Reset completed by user ID: ' . get_current_user_id());
?>
